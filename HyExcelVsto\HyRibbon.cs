﻿using ET;
using ET.ETLicense;
using HyExcelVsto.Module.Common;
using HyExcelVsto.Module.Wenzi;
using HyExcelVsto.Module.WX;
using HyExcelVsto.Module.WX.StationDataProcessor;
using HyExcelVsto.Module.WX.StationConverter;
using HyExcelVsto.Module.WX.AngleExtractor;
using HyExcelVsto.Module.WX.TowerAccountProcessor_JY;

//using HyExcelVsto.Module.AI;
using Microsoft.Office.Interop.Excel;
using Microsoft.Office.Tools.Ribbon;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Windows.Forms;
using System.Linq;

namespace HyExcelVsto
{
    /// <summary>
    /// Excel功能区(Ribbon)控制类
    /// </summary>
    /// <remarks>
    /// 此类提供以下主要功能：
    /// 1. 管理Excel功能区界面元素
    /// 2. 处理功能区按钮和控件的事件
    /// 3. 提供各种Excel增强功能的入口
    /// 4. 管理用户界面偏好设置
    ///
    /// 主要功能模块：
    /// - 文件操作（打开、保存、导入导出）
    /// - 单元格格式化和处理
    /// - 数据分析和处理
    /// - 自动化脚本执行
    /// - 辅助工具（GPS、字符处理等）
    ///
    /// 注意事项：
    /// - 所有UI控件事件都需要进行异常处理
    /// - 配置更改需要及时保存
    /// - 需要考虑Excel版本兼容性
    /// </remarks>
    public partial class HyRibbonClass : Microsoft.Office.Tools.Ribbon.RibbonBase
    {
        /// <summary>
        /// 配置文件实例
        /// </summary>
        private ETIniFile _iniFile;

        /// <summary>
        /// Excel应用程序实例
        /// </summary>
        public static Microsoft.Office.Interop.Excel.Application xlApp;

        /// <summary>
        /// 初始化 Ribbon 类
        /// </summary>
        /// <param name="factory">Ribbon 工厂实例</param>
        public HyRibbonClass(Microsoft.Office.Tools.Ribbon.RibbonFactory factory)
            : base(factory)
        {
            InitializeComponent();
            InitializeConfigurationSettings();
        }

        /// <summary>
        /// 初始化配置设置
        /// </summary>
        private void InitializeConfigurationSettings()
        {
            try
            {
                if (ThisAddIn.ConfigurationSettings != null)
                {
                    _iniFile = ThisAddIn.ConfigurationSettings;
                    ETLogManager.Info("配置文件实例初始化成功");
                }
                else
                {
                    ETLogManager.Warning("ThisAddIn.ConfigurationSettings 为空，配置文件实例初始化将延迟到加载时进行");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("初始化配置文件实例失败", ex);
            }
        }

        /// <summary>
        /// Ribbon加载事件处理方法
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 此方法在Ribbon加载时执行，主要完成以下工作：
        /// - 初始化控件权限映射和标题映射
        /// - 从配置文件加载用户界面设置
        /// - 更正控件标题为真正的标题
        /// </remarks>
        private void hyRibbon1_Load(object sender, RibbonUIEventArgs e)
        {
            try
            {
                // 确保配置文件实例已初始化
                if (ThisAddIn.ConfigurationSettings == null)
                {
                    ETLogManager.Warning("配置文件实例未初始化，跳过Ribbon界面设置的保存");
                    return;
                }

                // 如果配置文件实例未初始化，现在初始化它
                if (_iniFile == null)
                {
                    _iniFile = ThisAddIn.ConfigurationSettings;
                    ETLogManager.Info("配置文件实例已在加载时初始化");
                }

                // 关键：在Ribbon加载时立即保存原始控件标题，避免后续被混淆
                SaveOriginalControlTitles();

                // 注意：不在Ribbon加载时立即更正控件标题，避免在权限管理器初始化前就修改标题 控件标题的更正将在权限管理器初始化完成后通过OnPermissionManagerInitialized()方法进行
                ETLogManager.Info("Ribbon加载完成，原始标题已保存，等待权限管理器初始化后再更正控件标题");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("加载Ribbon界面失败", ex);
            }
        }

        #region 动态控件标题管理

        /// <summary>
        /// 在Ribbon加载时立即保存原始控件标题 关键：必须在任何权限检查和标题混淆之前调用
        /// </summary>
        private void SaveOriginalControlTitles()
        {
            try
            {
                ETLogManager.Info("开始保存原始控件标题（避免后续被混淆）");

                // 通知HyUIPermissionManager立即初始化全局映射 此时控件标题还是原始的，没有被混淆
                var uiManager = HyLicenseManager.UIPermissionManager as HyUIPermissionManager;
                if (uiManager != null)
                {
                    // 强制立即初始化全局映射，获取原始标题
                    uiManager.ForceInitializeGlobalMappings();
                    ETLogManager.Info("已通知权限管理器立即初始化全局映射");
                }
                else
                {
                    ETLogManager.Warning("UI权限管理器未初始化，无法保存原始控件标题");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("保存原始控件标题失败", ex);
            }
        }

        /// <summary>
        /// 动态获取当前Ribbon的所有控件引用 避免硬编码，通过反射获取所有控件
        /// </summary>
        /// <returns>控件名称到控件对象的映射</returns>
        private Dictionary<string, object> GetAllRibbonControlReferences()
        {
            var controlReferences = new Dictionary<string, object>();

            try
            {
                ETLogManager.Debug("开始动态获取Ribbon控件引用");

                // 通过反射获取当前Ribbon实例的所有字段
                var ribbonType = this.GetType();
                var fields = ribbonType.GetFields(
                    System.Reflection.BindingFlags.NonPublic |
                    System.Reflection.BindingFlags.Public |
                    System.Reflection.BindingFlags.Instance);

                ETLogManager.Debug($"🔍 HyRibbon反射获取到 {fields.Length} 个字段");

                // 🔍 特别关注znAbout相关字段
                var znAboutFields = fields.Where(f => f.Name.Contains("znAbout")).ToArray();
                ETLogManager.Info($"🔍 HyRibbon中发现 {znAboutFields.Length} 个znAbout相关字段: [{string.Join(", ", znAboutFields.Select(f => f.Name))}]");

                foreach (var field in fields)
                {
                    try
                    {
                        bool isZnAboutField = field.Name.Contains("znAbout");

                        if (isZnAboutField)
                        {
                            ETLogManager.Info($"🔍 HyRibbon处理znAbout字段: {field.Name}, 类型: {field.FieldType.FullName}");
                        }

                        // 检查字段是否是Ribbon控件
                        if (IsRibbonControlField(field))
                        {
                            var controlInstance = field.GetValue(this);
                            if (controlInstance != null)
                            {
                                controlReferences[field.Name] = controlInstance;
                                if (isZnAboutField)
                                {
                                    ETLogManager.Info($"🔍 znAbout字段 {field.Name} 控件实例获取成功，已添加到引用字典");
                                }
                            }
                            else if (isZnAboutField)
                            {
                                ETLogManager.Error($"🔍 znAbout字段 {field.Name} 控件实例为null！");
                            }
                        }
                        else if (isZnAboutField)
                        {
                            ETLogManager.Warning($"🔍 znAbout字段 {field.Name} 未通过Ribbon控件检查");
                        }
                    }
                    catch (Exception ex)
                    {
                        ETLogManager.Debug($"获取控件字段失败: {field.Name}", ex);
                    }
                }

                ETLogManager.Info($"动态获取到 {controlReferences.Count} 个Ribbon控件引用");

                // 🔍 验证znAbout控件是否在最终结果中
                var znAboutControls = controlReferences.Where(c => c.Key.Contains("znAbout")).ToArray();
                ETLogManager.Info($"🔍 HyRibbon最终控件引用中包含 {znAboutControls.Length} 个znAbout控件: [{string.Join(", ", znAboutControls.Select(c => c.Key))}]");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("动态获取Ribbon控件引用失败", ex);
            }

            return controlReferences;
        }

        /// <summary>
        /// 判断字段是否为Ribbon控件字段
        /// </summary>
        /// <param name="field">字段信息</param>
        /// <returns>是否为Ribbon控件</returns>
        private static bool IsRibbonControlField(System.Reflection.FieldInfo field)
        {
            var fieldType = field.FieldType;

            // 检查是否为Microsoft.Office.Tools.Ribbon命名空间下的控件
            return fieldType.Namespace == "Microsoft.Office.Tools.Ribbon" &&
                   (fieldType.Name.StartsWith("Ribbon") || fieldType.Name.Contains("Ribbon"));
        }

        #endregion 动态控件标题管理

        #region 控件标题更正方法

        /// <summary>
        /// 更正控件标题为真正的标题或模糊化标题
        /// </summary>
        /// <remarks>
        /// 此方法根据权限状态决定控件标题的显示：
        /// - 有权限：显示正常的中文标题
        /// - 无权限：显示4位随机英文乱码，隐藏功能 确保Excel在初始化时加载正常标题，运行时根据授权动态调整
        /// </remarks>
        public void CorrectControlTitles()
        {
            try
            {
                ETLogManager.Info("开始动态更正控件标题（避免硬编码）");

                // 使用权限管理器的批量更新功能
                if (HyLicenseManager.UIPermissionManager != null)
                {
                    // 获取当前Ribbon的所有控件并批量更新
                    var controlReferences = GetAllRibbonControlReferences();
                    HyLicenseManager.UIPermissionManager.BatchUpdateControlTitles(controlReferences);

                    ETLogManager.Info($"动态批量更新完成，共更新 {controlReferences.Count} 个控件");
                }
                else
                {
                    ETLogManager.Warning("权限管理器未初始化，无法进行批量控件标题更新");
                }

                ETLogManager.Info("控件标题更正完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("更正控件标题失败", ex);
            }
        }

        /// <summary>
        /// 网络授权更新后的回调方法（由HyUIPermissionManager调用）
        /// </summary>
        /// <remarks>当用户通过About对话框更新授权信息后，权限管理器会调用此方法 根据最新的权限状态重新设置所有控件标题</remarks>
        public void OnNetworkLicenseUpdated()
        {
            try
            {
                ETLogManager.Info("网络授权已更新，开始刷新控件标题");

                // 确保权限管理器已经初始化
                if (HyLicenseManager.UIPermissionManager == null)
                {
                    ETLogManager.Warning("权限管理器未初始化，使用备用方法刷新控件标题");
                    // 备用方案：使用原有的逐个更新方法
                    CorrectControlTitles();
                    return;
                }

                // 使用新的批量刷新功能
                HyLicenseManager.UIPermissionManager.RefreshRibbonControlTitles();

                ETLogManager.Info("控件标题刷新完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("网络授权更新后刷新控件标题失败", ex);

                // 出错时使用备用方案
                try
                {
                    ETLogManager.Info("使用备用方案刷新控件标题");
                    CorrectControlTitles();
                }
                catch (Exception fallbackEx)
                {
                    ETLogManager.Error("备用方案刷新控件标题也失败", fallbackEx);
                }
            }
        }

        /// <summary>
        /// 刷新控件标题（权限状态变更后调用）
        /// </summary>
        /// <remarks>根据最新的权限状态重新设置所有控件标题</remarks>
        public void RefreshControlTitles()
        {
            try
            {
                ETLogManager.Info("开始刷新控件标题");

                // 使用新的控件权限管理器刷新控件标题
                if (HyLicenseManager.UIPermissionManager != null)
                {
                    HyLicenseManager.UIPermissionManager.RefreshControlTitles();
                }

                // 重新设置所有控件标题（基于最新权限状态）
                CorrectControlTitles();

                ETLogManager.Info("控件标题刷新完成");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("刷新控件标题失败", ex);
            }
        }

        /// <summary>
        /// 权限管理器初始化完成通知方法
        /// </summary>
        /// <remarks>
        /// 此方法在权限管理器初始化完成后被调用，用于：
        /// 1. 确保Ribbon控件的权限状态得到正确更新
        /// 2. 刷新控件标题以反映最新的权限状态
        /// 3. 记录权限管理器初始化完成的日志
        /// </remarks>
        public void OnPermissionManagerInitialized()
        {
            try
            {
                ETLogManager.Info("收到权限管理器初始化完成通知");

                // 刷新控件标题以反映最新的权限状态
                RefreshControlTitles();

                ETLogManager.Info("权限管理器初始化完成处理结束");
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理权限管理器初始化完成通知时出错", ex);
            }
        }

        #endregion 控件标题更正方法

        #region hyTab

        /// <summary>
        /// 处理剪贴板监控设置
        /// </summary>
        private void checkBoxHy监控剪贴板_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                if (!checkBox监控剪贴板.Checked)
                {
                    ETForm.ShowHide菜单子项(new[] { "粘贴工参" }, false);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置剪贴板监控失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 控制Excel中0值的显示
        /// </summary>
        private void checkBoxHy显示0值_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                xlApp.ActiveWindow.DisplayZeros = chk显示0值.Checked;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置0值显示失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开批量查找窗体
        /// </summary>
        private void buttonZnHy批量查找_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                frm批量查找 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Right);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开批量查找窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 执行标签筛选输入工具
        /// </summary>
        private void buttonHy标签填写筛选_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                bool isProcessed = true;
                HyFunctions.Button标签筛选输入工具_Click(null, ref isProcessed);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"执行标签筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 控制分级标记的显示
        /// </summary>
        private void checkBoxHy分级标记_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                xlApp.ActiveWindow.DisplayOutline = checkBox分级标记.Checked;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置分级标记显示失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 控制叠加显示辅助功能
        /// </summary>
        private void checkBoxHy叠加显示辅助_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                if (checkBox叠加显示辅助.Checked)
                {
                    ThisAddIn.LoadHelpForm();
                }
                else
                {
                    ThisAddIn.HideToolsWindows();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"控制叠加显示辅助失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 控制水平高亮显示
        /// </summary>
        private void checkBoxZn水平高亮行列_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                显示行列高亮控制();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"控制水平高亮显示失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 控制垂直高亮显示
        /// </summary>
        private void checkBoxZn垂直高亮行列_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                显示行列高亮控制();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"控制垂直高亮显示失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 控制行列高亮显示
        /// </summary>
        /// <remarks>此方法根据水平和垂直高亮的选中状态， 控制高亮覆盖层的显示和隐藏。</remarks>
        private void 显示行列高亮控制()
        {
            try
            {
                if (checkBoxHorizontalHighlight.Checked || checkBoxVerticalHighlight.Checked)
                {
                    ThisAddIn.LoadHighlightOverlayForm();
                }
                else
                {
                    ThisAddIn.CrosshairOverlayForm.HideWindow();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"控制行列高亮显示失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开WPS/Excel切换窗体
        /// </summary>
        private void buttonHyWpsExcel切换_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                frmWpsExce切换 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                //弹出错误提示框
                MessageBox.Show("打开WPS/Excel切换窗体失败！\n" + ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开数据同步窗体
        /// </summary>
        private void buttonHy同步数据_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                frm填表同步数据 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);
            }
            catch (Exception ex)
            {
                //弹出错误提示框
                MessageBox.Show("打开数据同步窗体失败！\n" + ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开合规检查窗体
        /// </summary>
        private void buttonZnHy填写合规检查_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                frm合规检查 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开合规检查窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开AI辅助填写窗体
        /// </summary>
        private void buttonHyAI辅助填写_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                //frmAIv2 frm = new();
                //ThisAddIn.OpenForm(frm, XlFormPosition.Center, true);
            }
            catch (Exception ex)
            {
                //弹出错误提示框
                MessageBox.Show("打开AI辅助填写窗体失败！\n" + ex.Message, "提示", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion hyTab

        #region hyTab2

        #region 脚本

        /// <summary>
        /// 加载脚本内容到Gallery控件
        /// </summary>
        /// <remarks>
        /// 此方法从Excel工作表中读取脚本配置信息， 并将其加载到Gallery控件中显示。 支持两种类型的脚本：
        /// - autoExecute: 自动执行脚本
        /// - autoUpdate: 自动更新脚本
        /// </remarks>
        private void gallery脚本内容_ItemsLoading(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                //if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                //{
                //    return;
                //}

                gallery脚本内容.Items.Clear();

                HashSet<string> menuItems = [];
                int iIndex = 0;

                // 读取 "autoExecute" 工作表内容
                int autoExecuteCount = ReadWorksheetContent("autoExecute", "A2:B1000", menuItems, ref iIndex);
                if (autoExecuteCount > 0)
                {
                    AddMenuItem("-----autoExecute-----", "autoExecute");
                    foreach (string item in menuItems)
                    {
                        AddMenuItem(item, "autoExecute");
                    }
                }

                menuItems.Clear();

                // 读取 "autoUpdate" 工作表内容
                int autoUpdateCount = ReadWorksheetContent("autoUpdate", "A5:B1000", menuItems, ref iIndex);
                if (autoUpdateCount > 0)
                {
                    AddMenuItem("-----autoUpdate-----", "autoUpdate");
                    foreach (string item in menuItems)
                    {
                        AddMenuItem(item, "autoUpdate");
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载脚本内容失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 读取工作表内容并填充菜单项
        /// </summary>
        /// <param name="sheetName">工作表名称</param>
        /// <param name="range">要读取的单元格范围</param>
        /// <param name="menuItems">存储菜单项的集合</param>
        /// <param name="iIndex">索引引用</param>
        /// <returns>读取到的有效菜单项数量</returns>
        /// <remarks>
        /// 此方法会：
        /// 1. 检查工作表是否存在
        /// 2. 读取指定范围的单元格内容
        /// 3. 根据单元格内容和状态添加菜单项
        /// 4. 跳过被禁用的项目
        /// </remarks>
        private int ReadWorksheetContent(string sheetName, string range, HashSet<string> menuItems, ref int iIndex)
        {
            try
            {
                Worksheet worksheet = xlApp.ActiveWorkbook.GetWorksheetByName(sheetName);
                if (worksheet == null)
                    return 0;

                Range rng = ETExcelExtensions.OptimizeRangeSize(worksheet.Range[range]);
                int validItemCount = 0;

                for (int i = 1; i <= rng.Rows.Count; i++)
                {
                    dynamic cell = rng.Cells[i, 1];
                    dynamic statusCell = rng.Cells[i, 2];

                    if (!ETExcelExtensions.IsCellEmpty(cell))
                    {
                        string strValue = cell.Value.ToString().Trim();
                        bool isEnabled = ETExcelExtensions.IsCellEmpty(statusCell) ||
                            statusCell.Value.ToString().Trim().ToLower() == "启用";

                        if (isEnabled)
                        {
                            if (!strValue.StartsWith("--") && !strValue.StartsWith("=="))
                            {
                                menuItems.Add(strValue);
                                validItemCount++;
                            }
                            else if (strValue.StartsWith("--"))
                            {
                                menuItems.Add("-----");
                                // 分隔线不计入有效项数量
                            }
                        }
                    }
                }

                return validItemCount;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"读取工作表内容失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return 0; // 异常情况下返回0，表示没有读取到有效项
            }
        }

        /// <summary>
        /// 添加菜单项到Gallery控件
        /// </summary>
        /// <param name="groupName">菜单项名称或分组名称</param>
        /// <param name="sheetName">关联的工作表名称</param>
        /// <remarks>
        /// 此方法会：
        /// 1. 检查菜单项名称的有效性
        /// 2. 创建新的下拉项
        /// 3. 设置项目的标签和标记
        /// 4. 处理分隔符的特殊显示
        /// </remarks>
        private void AddMenuItem(string groupName, string sheetName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(groupName) || groupName.StartsWith("=="))
                    return;

                RibbonDropDownItem downItem = Factory.CreateRibbonDropDownItem();
                gallery脚本内容.Items.Add(downItem);

                if (string.IsNullOrEmpty(groupName) || groupName == "-----")
                {
                    downItem.Label = "              ";
                    downItem.Tag = "分隔符";
                }
                else
                {
                    downItem.Label = groupName;
                    downItem.Tag = $"{sheetName}|{groupName}";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加菜单项失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理脚本内容选择事件
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 解析选中项的标记信息
        /// 2. 创建并显示自动脚本窗体
        /// 3. 执行选中的脚本
        /// </remarks>
        private void gallery脚本内容_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                //if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                //{
                //    return;
                //}

                string tag = gallery脚本内容.SelectedItem.Tag.ToString();

                if (string.IsNullOrWhiteSpace(tag) || tag.StartsWith("---"))
                    return;

                // 将Tag拆分成数组
                string[] tagParts = tag.Split('|');

                // 确保数组至少有两个元素
                if (tagParts.Length < 2)
                    return;

                string sheetName = tagParts[0];
                string groupName = tagParts[1];

                frm自动脚本 frm = new(false, 10000);
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);

                // 调用Run执行脚本方法，传入groupName和tableName
                frm.Run执行脚本(groupName, sheetName);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"执行脚本失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开脚本控制表
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 创建并初始化自动脚本窗体
        /// 2. 显示控制工作表
        /// 3. 激活autoExecute工作表
        /// </remarks>
        private void button打开脚本表_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                //if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                //{
                //    return;
                //}

                using (frm自动脚本 frm = new(false))
                {
                    ThisAddIn.OpenForm(frm);
                    frm.初始化();
                    ShowScriptControlSheets(frm);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开脚本表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示脚本控制工作表
        /// </summary>
        /// <param name="scriptForm">自动脚本窗体实例</param>
        private void ShowScriptControlSheets(frm自动脚本 scriptForm)
        {
            if (scriptForm.autoExecuteControlWorksheet != null)
            {
                scriptForm.autoExecuteControlWorksheet.Visible = XlSheetVisibility.xlSheetVisible;
                scriptForm.autoExecuteControlWorksheet.Activate();
            }
            if (scriptForm.autoUpdateControlSheet != null)
            {
                scriptForm.autoUpdateControlSheet.Visible = XlSheetVisibility.xlSheetVisible;
            }
        }

        #endregion 脚本

        /// <summary>
        /// 打开工作表管理窗体
        /// </summary>
        private void button工作表管理_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!(HyLicenseManager.HasPermission(HyPermissionKeys.Develop) || HyLicenseManager.HasPermission(HyPermissionKeys.Hy)))
                {
                    return;
                }

                frm工作表管理 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开工作表管理窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void checkBoxStockHelper_Click(object sender, RibbonControlEventArgs e)
        {
            GlobalSettings.IsStockDataHelperEnabled = checkBoxStockHelper.Checked;
        }

        /// <summary>
        /// 打开考勤处理窗体
        /// </summary>
        private void button考勤_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!(HyLicenseManager.HasPermission(HyPermissionKeys.Develop) || HyLicenseManager.HasPermission(HyPermissionKeys.Hy)))
                {
                    return;
                }

                frm考勤 frm = new();
                ThisAddIn.OpenForm(frm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开考勤处理窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion hyTab2

        #region znTab & hyTab

        #region Hy常用文件列表

        /// <summary>
        /// 加载常用文件列表
        /// </summary>
        private void gallery常用文件_ItemsLoading(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                UpdateGallery常用文件();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"加载常用文件列表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 更新常用文件列表
        /// </summary>
        /// <remarks>
        /// 此方法使用通用的ETForm.LoadRibbonGalleryFromConfig方法从配置文件中读取常用文件列表， 并将其加载到Gallery控件中显示。 配置文件格式：每行一个文件，格式为"显示名称=文件路径"
        /// </remarks>
        private void UpdateGallery常用文件()
        {
            try
            {
                ETForm.LoadRibbonGalleryFromConfig(gallery常用文件, "收藏文件.config", Factory, new System.Action(UpdateGallery常用文件));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"更新常用文件列表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理常用文件选择事件
        /// </summary>
        /// <remarks>
        /// 此方法使用最简化的通用处理方法：
        /// - 管理功能按钮（打开配置文件、刷新列表）完全自动处理
        /// - 文件打开逻辑完全通用化处理
        /// - 无需编写任何业务逻辑代码
        /// </remarks>
        private void Button常用文件_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    return;
                }

                string tagValue = gallery常用文件.SelectedItem.Tag as string;

                // 使用最简化的完整处理方法，一行代码搞定所有逻辑
                ETForm.HandleRibbonGalleryClickComplete(gallery常用文件, tagValue, xlApp);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"处理常用文件操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion Hy常用文件列表

        /// <summary>
        /// 删除工作簿外部链接
        /// </summary>
        private void buttonZnHy删除外部链接_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                if (!ETExcelExtensions.VerifyCode("确认删除外部链接吗?(本操作不可回退)"))
                    return;

                ETExcelExtensions.Delete外部连接(xlApp.ActiveWorkbook);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"删除外部链接失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 配置工作表大纲设置
        /// </summary>
        /// <param name="worksheet">要配置的工作表</param>
        private void ConfigureWorksheetOutline(Worksheet worksheet)
        {
            worksheet.Outline.AutomaticStyles = false;
            worksheet.Outline.SummaryRow = XlSummaryRow.xlSummaryAbove;
            worksheet.Outline.SummaryColumn = XlSummaryColumn.xlSummaryOnLeft;
        }

        /// <summary>
        /// Excel修复功能
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 调用Excel修复函数
        /// 2. 设置工作表大纲样式
        /// 3. 配置汇总行和列的位置
        /// </remarks>
        private void buttonZnHyExcel修复_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                HyFunctions.BtnExcel修复();

                Worksheet activeSheet = xlApp.ActiveSheet;
                ConfigureWorksheetOutline(activeSheet);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Excel修复操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 重置单元格备注的大小
        /// </summary>
        private void buttonZnHy重置单元格备注大小_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                ETExcelExtensions.ResetCellsNotesSize();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"重置单元格备注大小失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 复制当前工作簿的文件路径
        /// </summary>
        private void buttonHy复制当前文件路径_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                Workbook activeWorkbook = xlApp.ActiveWorkbook;

                if (activeWorkbook != null)
                {
                    string filePath = activeWorkbook.FullName;
                    System.Windows.Forms.Clipboard.SetText(filePath);
                    MessageBox.Show($"已复制文件路径到剪贴板。\r\n{filePath}", "提示", MessageBoxButtons.OK);
                }
                else
                {
                    MessageBox.Show("没有打开的工作簿。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"复制文件路径失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开备份及发送窗体
        /// </summary>
        private void buttonZnHy发送及存档_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm备份及发送 frm = new();
                ThisAddIn.OpenForm(frm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开备份及发送窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开页眉页脚设置窗体
        /// </summary>
        private void buttonZnHy设置页眉脚_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm设置页眉页脚 frm = new();
                ThisAddIn.OpenForm(frm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开页眉页脚设置窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 执行金额转大写功能
        /// </summary>
        private void buttonHy金额转大写_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                HyFunctions.Btn金额转大写();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"金额转大写失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 隐藏选定范围外的内容
        /// </summary>
        private void buttonHy隐藏范围外内容_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                if (!ETExcelExtensions.VerifyCode("隐藏范围外内容吗?  ")) return;

                HyFunctions.Btn隐藏范围外内容();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"隐藏范围外内容失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开经纬度格式化窗体
        /// </summary>
        private void buttonZnHy格式化经纬度_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm格式化经纬度 frm = new()
                {
                    XlApp = xlApp
                };
                ThisAddIn.OpenForm(frm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开经纬度格式化窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清除所选区域的条件格式并撤销筛选
        /// </summary>
        private void buttonZnHy清除所选条件格式及全表筛选_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                Worksheet activeSheet = xlApp.ActiveSheet;
                bool isProcessed = true;
                HyFunctions.button清除所选条件格式_Click(null, ref isProcessed);
                activeSheet.Filter撤销所有筛选条件();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清除条件格式和筛选失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清除所选区域的条件格式
        /// </summary>
        private void buttonZnHy清除所选条件格式_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                bool isProcessed = true;
                HyFunctions.button清除所选条件格式_Click(null, ref isProcessed);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清除条件格式失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理关于按钮点击事件，用于授权码输入
        /// </summary>
        /// <remarks>
        /// 此方法执行以下操作：
        /// 1. 提示用户输入授权码
        /// 2. 保存授权码到配置文件
        /// 3. 添加加密授权
        /// 4. 检查按钮授权状态
        /// </remarks>
        private void buttonZnHyAbout_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 正常模式：显示关于对话框
                ETAboutLicenseForm aboutForm = new()
                {
                    // 设置软件版本和授权说明信息
                    SoftwareVersion = "1.0.0",
                    LicenseDescription = "该插件是一款Excel增强工具。\n\n版权所有，仅供内部使用。",
                    // 传递授权控制器实例，用于更新授权功能
                    LicenseController = ThisAddIn.LicenseController
                };

                // 显示窗体
                aboutForm.ShowDialog();
            }
            catch (Exception ex)
            {
                ETLogManager.Error("处理关于按钮点击事件失败", ex);
                System.Windows.Forms.MessageBox.Show($"操作失败: {ex.Message}", "错误",
                    System.Windows.Forms.MessageBoxButtons.OK,
                    System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 清除整个工作表的条件格式
        /// </summary>
        private void buttonyHy清除全表条件格式_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                bool isProcessed = true;
                HyFunctions.button清除全表条件格式_Click(null, ref isProcessed);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"清除全表条件格式失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开GPS站点查找窗体
        /// </summary>
        private void buttonZn批量查找站点_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm查找站点 frm = new();

                //隐藏多边形查找标签页
                frm.tabPage查找多边形.Parent = null;

                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开GPS站点查找窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开GPS站点查找窗体
        /// </summary>
        private void buttonHy批量查找站点_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm查找站点 frm = new();

                //显示多边形查找标签页
                frm.tabPage查找多边形.Parent = frm.tabControl1;

                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开GPS站点查找窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开自动脚本窗体
        /// </summary>
        private void buttonHy自动脚本_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm自动脚本 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Right);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开自动脚本窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 设置行高倍数
        /// </summary>
        private void buttonZnHy设置倍数行高_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                bool isProcessed = false;
                HyFunctions.Btn设置倍数行高(null, ref isProcessed);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置行高倍数失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开无线小工具窗体
        /// </summary>
        private void buttonHy无线小工具_Click(object sender, RibbonControlEventArgs e)
        {
        }

        /// <summary>
        /// 基站数据处理器按钮点击事件
        /// </summary>
        private void btnStationDataProcessor_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var frm = new HyExcelVsto.Module.WX.StationDataProcessor.StationDataProcessorForm();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开基站数据处理器窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 站点数据转换器按钮点击事件
        /// </summary>
        private void btnStationConverter_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var frm = new HyExcelVsto.Module.WX.StationConverter.StationConverterForm();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开站点数据转换器窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 方向角/下倾角提取器按钮点击事件
        /// </summary>
        private void btnAngleExtractor_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var frm = new HyExcelVsto.Module.WX.AngleExtractor.AngleExtractorForm();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开方向角/下倾角提取器窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 铁塔内部台账梳理按钮点击事件
        /// </summary>
        private void btnTowerAccountProcessor_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var frm = new HyExcelVsto.Module.WX.TowerAccountProcessor_JY.TowerAccountProcessorForm();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, false);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开铁塔内部台账梳理窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开字符处理窗体的标记重复值标签页
        /// </summary>
        private void buttonZnHy字符串处理_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm字符处理 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开标记重复值窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开Visio助手窗体
        /// </summary>
        public void buttonHyVisioHelper_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frmVisioHelper frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开Visio助手窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void buttonZnHyini配置文件_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                string configPath = ETConfig.GetConfigDirectory("config.ini");
                if (File.Exists(configPath))
                {
                    Process.Start(configPath);
                }
                else
                {
                    MessageBox.Show("配置文件不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    ETLogManager.Error($"配置文件不存在：{configPath}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开配置文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开向下填充窗体
        /// </summary>
        private void buttonHy向下填充_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm向下填充 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.NoControl);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开向下填充窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开配置文件所在目录
        /// </summary>
        private void buttonZnHy配置目录_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 获取配置文件完整路径
                string configFilePath = ETConfig.GetConfigDirectory();

                if (!string.IsNullOrEmpty(configFilePath) && Directory.Exists(configFilePath))
                {
                    // 打开目录
                    Process.Start(configFilePath);
                }
                else
                {
                    MessageBox.Show("配置目录不存在", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    ETLogManager.Error($"配置目录不存在：{configFilePath}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开配置目录失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开GPS图层生成窗体
        /// </summary>
        private void buttonZnHy生成地理图层_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frmGPS生成图层 frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开GPS图层生成窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开51自动上传窗体 - V2现代化版本
        /// </summary>
        private void buttonHy51小工具V2_Click(object sender, RibbonControlEventArgs e)
        {
            //try
            //{
            //    var frm = new HyExcelVsto.Module.WX.Helper51V2.UI.MainForm();
            //    ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"打开51自动上传窗体V2失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
        }

        #endregion znTab & hyTab

        #region znTab2

        /// <summary>
        /// 打开Word助手窗体，显示Word替换标签页
        /// </summary>
        private void buttonZnHyWord助手_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frmWordHelper frm = new();
                frm.tabControl1.SelectedTab = frm.tabPage替换Word;
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开Word助手窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开PPT助手窗体，显示PPT替换标签页
        /// </summary>
        private void buttonZnHyPPT助手_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frmPPTHelper frm = new();
                frm.tabControl1.SelectedTab = frm.tabPage替换PPT;
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开PPT助手窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void buttonZn文件助手_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm文件操作 frm = new();
                frm.tabControl1.SelectedTab = frm.tabPage检查文件;

                // 隐藏无权限的标签页
                frm.tabPage文件改时间.Parent = null;

                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
                ThisAddIn.OpenForm(frm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件操作窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void buttonHy文件助手_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                frm文件操作 frm = new();
                frm.tabControl1.SelectedTab = frm.tabPage检查文件;

                // 显示改时间的标签页
                frm.tabPage文件改时间.Parent = frm.tabControl1;

                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
                ThisAddIn.OpenForm(frm);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件操作窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion znTab2

        private void buttonZnHy51小工具V1_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var frm = new frm51Helper();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开51自动上传窗体V2失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void buttonZnHy最近打开文件_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var frm = new frmExcelFileManager();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开Excel文件记录管理失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 记录当前文件按键点击事件
        /// </summary>
        private void button记录当前文件_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 创建文件管理器实例
                var frm = new frmExcelFileManager();

                // 调用记录当前文件的公共方法
                int recordCount = frm.RecordCurrentFiles(false);

                // 显示操作结果
                if (recordCount > 0)
                {
                    MessageBox.Show($"成功记录 {recordCount} 个当前打开的Excel文件", "记录完成",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (recordCount == 0)
                {
                    MessageBox.Show("没有找到可记录的Excel文件", "提示",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                // recordCount == -1 时，RecordCurrentFiles方法内部已经显示了错误消息

                // 释放窗体资源（不显示窗体）
                frm.Dispose();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"记录当前文件失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 开发测试按钮点击事件 - 导出OfficeImageId和对应图标
        /// </summary>
        /// <param name="sender">事件发送者</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 此方法提供以下功能：
        /// 1. 导出所有可用的OfficeImageId到Excel文件
        /// 2. 生成图标预览HTML文件
        /// 3. 提供图标ID的中文描述和分类信息
        /// 4. 帮助开发人员快速查找和使用Office图标
        ///
        /// 导出内容包括：
        /// - 图标ID名称
        /// - 中文描述
        /// - 使用频率
        /// - 分类信息
        /// - 使用建议
        /// </remarks>
        private void buttonDevelopTest_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 权限检查：需要开发权限
                if (!HyLicenseManager.HasPermission(HyPermissionKeys.Develop))
                {
                    MessageBox.Show("此功能需要开发权限，请联系管理员获取授权。",
                        "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 显示确认对话框
                var result = MessageBox.Show(
                    "是否导出所有OfficeImageId和对应图标到Excel文件？\n\n" +
                    "导出内容包括：\n" +
                    "• 自动枚举或从配置文件读取的所有图标ID\n" +
                    "• 使用GetImageMso方法提取的真实Office图标\n" +
                    "• 智能生成的中文描述和功能分类\n" +
                    "• 32x32像素的高质量图标预览\n\n" +
                    "数据源：自动枚举 + 智能缓存\n" +
                    "技术：基于GetImageMso方法\n" +
                    "保存路径：E:\\Temp\\工作临时目录\\2025-07-30-08-37",
                    "OfficeImageId导出工具",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 导出到Excel文件
                    OfficeImageIdExportHelper.ExportOfficeImageIdsToExcel();
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"导出OfficeImageId失败: {ex.Message}");
                MessageBox.Show($"导出OfficeImageId功能执行失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void button订单文件生成kml图层_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                var frm = new HyExcelVsto.Module.WX.OrderKmlGenerator.UI.OrderKmlGeneratorForm();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开DevelopTest失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void button铁塔KML点图转换_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                HyExcelVsto.Module.WX.KmlConverter.frmKmlConverter frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开铁塔KML点图转换失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void button多边形GPS坐标转换器_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                HyExcelVsto.Module.WX.PolygonGpsConverter.frmPolygonGpsConverter frm = new();
                ThisAddIn.OpenForm(frm, XlFormPosition.Center, true);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开多边形GPS坐标转换器失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开文件按钮点击事件处理
        /// </summary>
        /// <param name="sender">事件源</param>
        /// <param name="e">事件参数</param>
        /// <remarks>
        /// 此方法执行以下逻辑：
        /// 1. 首先检测剪贴板中是否包含有效的Excel文件路径（文本形式）
        /// 2. 如果不是文本路径，则检测剪贴板中是否有复制的文件，并判断是否为Excel文件
        /// 3. 如果检测到有效的Excel文件，弹出确认对话框询问是否打开剪贴板中的文件
        /// 4. 如果用户选择是，则打开剪贴板中的文件
        /// 5. 如果用户选择否或剪贴板中没有有效Excel文件，则显示文件选择对话框
        /// 6. 打开用户选择的Excel文件
        /// </remarks>
        private void button打开文件_Click(object sender, RibbonControlEventArgs e)
        {
            try
            {
                // 检测剪贴板中的内容
                var clipboardResult = ETFile.GetClipboardExcelFilePath();

                if (clipboardResult.IsValid)
                {
                    // 剪贴板中包含有效的Excel文件路径，询问用户是否打开
                    string message = clipboardResult.GetUserMessage();
                    DialogResult result = MessageBox.Show(
                        message,
                        "打开文件确认",
                        MessageBoxButtons.YesNo,
                        MessageBoxIcon.Question);

                    if (result == DialogResult.Yes)
                    {
                        // 用户选择打开剪贴板中的文件
                        OpenExcelFile(clipboardResult.FilePath);
                        return;
                    }
                }

                // 用户选择否或剪贴板中没有有效Excel文件路径，显示文件选择对话框
                ShowExcelFileDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"打开文件操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 打开指定的Excel文件
        /// </summary>
        /// <param name="filePath">Excel文件路径</param>
        private void OpenExcelFile(string filePath)
        {
            try
            {
                ETLogManager.Info($"正在打开Excel文件：{filePath}");

                // 使用Excel应用程序打开文件
                Workbook workbook = xlApp.Workbooks.Open(filePath);

                if (workbook != null)
                {
                    ETLogManager.Info($"成功打开Excel文件：{filePath}");
                    //MessageBox.Show($"文件已成功打开：\n{Path.GetFileName(filePath)}",
                    //"打开成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    throw new Exception("工作簿对象为空");
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error($"打开Excel文件失败：{filePath}", ex);
                MessageBox.Show($"打开文件失败：\n{ex.Message}",
                    "打开失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 显示Excel文件选择对话框
        /// </summary>
        private void ShowExcelFileDialog()
        {
            try
            {
                using (OpenFileDialog openFileDialog = new OpenFileDialog())
                {
                    // 设置对话框属性
                    openFileDialog.Title = "选择Excel文件";
                    openFileDialog.Filter = "Excel文件|*.xls;*.xlsx;*.xlsm;*.xlsb;*.xltx;*.xltm;*.xlt|" +
                                          "Excel 2007-2019文件 (*.xlsx)|*.xlsx|" +
                                          "Excel 97-2003文件 (*.xls)|*.xls|" +
                                          "Excel启用宏的工作簿 (*.xlsm)|*.xlsm|" +
                                          "Excel二进制工作簿 (*.xlsb)|*.xlsb|" +
                                          "Excel模板 (*.xltx)|*.xltx|" +
                                          "Excel启用宏的模板 (*.xltm)|*.xltm|" +
                                          "Excel 97-2003模板 (*.xlt)|*.xlt|" +
                                          "所有文件|*.*";
                    openFileDialog.FilterIndex = 1;
                    openFileDialog.RestoreDirectory = true;
                    openFileDialog.Multiselect = false;

                    // 设置初始目录为文档文件夹
                    openFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);

                    // 显示对话框
                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        string selectedFile = openFileDialog.FileName;
                        ETLogManager.Info($"用户选择了文件：{selectedFile}");

                        // 打开选择的文件
                        OpenExcelFile(selectedFile);
                    }
                    else
                    {
                        ETLogManager.Info("用户取消了文件选择");
                    }
                }
            }
            catch (Exception ex)
            {
                ETLogManager.Error("显示文件选择对话框失败", ex);
                MessageBox.Show($"打开文件选择对话框失败：\n{ex.Message}",
                    "操作失败", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}